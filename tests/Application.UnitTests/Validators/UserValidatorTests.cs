using FluentAssertions;
using Application.Validators;
using NUnit.Framework;

namespace Application.UnitTests.Validators;

[TestFixture]
public class RegisterUserRequestValidatorTests
{
    private RegisterUserRequestValidator _validator;

    [SetUp]
    public void SetUp()
    {
        _validator = new RegisterUserRequestValidator();
    }

    [Test]
    public async Task Validate_WhenAllFieldsValid_ShouldReturnValid()
    {
        // Arrange
        var request = new RegisterUserRequest
        {
            Email = "<EMAIL>",
            Password = "SecurePassword123!"
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Test]
    public async Task Validate_WhenFirstNameEmpty_ShouldReturnInvalid()
    {
        // Arrange
        var request = new RegisterUserRequest
        {
            Email = "<EMAIL>",
            Password = "SecurePassword123!"
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().ContainSingle(e => e.PropertyName == "FirstName");
    }

    [Test]
    public async Task Validate_WhenLastNameEmpty_ShouldReturnInvalid()
    {
        // Arrange
        var request = new RegisterUserRequest
        {
            Email = "<EMAIL>",
            Password = "SecurePassword123!"
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().ContainSingle(e => e.PropertyName == "LastName");
    }

    [Test]
    public async Task Validate_WhenEmailEmpty_ShouldReturnInvalid()
    {
        // Arrange
        var request = new RegisterUserRequest
        {
            Email = "",
            Password = "SecurePassword123!"
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Where(e => e.PropertyName == "Email").Should().HaveCount(2);
        result.Errors.Should().Contain(e => e.PropertyName == "Email" && e.ErrorMessage == "Email is required");
        result.Errors.Should().Contain(e => e.PropertyName == "Email" && e.ErrorMessage == "A valid email is required");
    }

    [Test]
    public async Task Validate_WhenEmailInvalid_ShouldReturnInvalid()
    {
        // Arrange
        var request = new RegisterUserRequest
        {
            Email = "invalid-email",
            Password = "SecurePassword123!"
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().ContainSingle(e => e.PropertyName == "Email");
    }

    [Test]
    public async Task Validate_WhenPasswordEmpty_ShouldReturnInvalid()
    {
        // Arrange
        var request = new RegisterUserRequest
        {
            Email = "<EMAIL>",
            Password = ""
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Where(e => e.PropertyName == "Password").Should().HaveCount(3);
        result.Errors.Should().Contain(e => e.PropertyName == "Password" && e.ErrorMessage == "Password is required");
        result.Errors.Should().Contain(e => e.PropertyName == "Password" && e.ErrorMessage == "Password must be at least 6 characters");
        result.Errors.Should().Contain(e => e.PropertyName == "Password" && e.ErrorMessage == "Password must contain at least one lowercase letter, one uppercase letter, and one digit");
    }

    [Test]
    public async Task Validate_WhenPasswordTooShort_ShouldReturnInvalid()
    {
        // Arrange
        var request = new RegisterUserRequest
        {
            Email = "<EMAIL>",
            Password = "123" // Too short
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Where(e => e.PropertyName == "Password").Should().HaveCount(2);
        result.Errors.Should().Contain(e => e.PropertyName == "Password" && e.ErrorMessage == "Password must be at least 6 characters");
        result.Errors.Should().Contain(e => e.PropertyName == "Password" && e.ErrorMessage == "Password must contain at least one lowercase letter, one uppercase letter, and one digit");
    }

    [Test]
    public async Task Validate_WhenMultipleFieldsInvalid_ShouldReturnMultipleErrors()
    {
        // Arrange
        var request = new RegisterUserRequest
        {
            Email = "invalid",
            Password = "123"
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().HaveCount(5); // Expecting 5 errors: FirstName, LastName, Email, Password (length), Password (complexity)
        result.Errors.Should().Contain(e => e.PropertyName == "FirstName");
        result.Errors.Should().Contain(e => e.PropertyName == "LastName");
        result.Errors.Should().Contain(e => e.PropertyName == "Email");
        result.Errors.Should().Contain(e => e.PropertyName == "Password");
    }
}

[TestFixture]
public class LoginUserRequestValidatorTests
{
    private LoginUserRequestValidator _validator;

    [SetUp]
    public void SetUp()
    {
        _validator = new LoginUserRequestValidator();
    }

    [Test]
    public async Task Validate_WhenAllFieldsValid_ShouldReturnValid()
    {
        // Arrange
        var request = new LoginUserRequest
        {
            Email = "<EMAIL>",
            Password = "SecurePassword123!"
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Test]
    public async Task Validate_WhenEmailEmpty_ShouldReturnInvalid()
    {
        // Arrange
        var request = new LoginUserRequest
        {
            Email = "",
            Password = "SecurePassword123!"
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Where(e => e.PropertyName == "Email").Should().HaveCount(2);
        result.Errors.Should().Contain(e => e.PropertyName == "Email" && e.ErrorMessage == "Email is required");
        result.Errors.Should().Contain(e => e.PropertyName == "Email" && e.ErrorMessage == "A valid email is required");
    }

    [Test]
    public async Task Validate_WhenEmailInvalid_ShouldReturnInvalid()
    {
        // Arrange
        var request = new LoginUserRequest
        {
            Email = "invalid-email",
            Password = "SecurePassword123!"
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().ContainSingle(e => e.PropertyName == "Email");
    }

    [Test]
    public async Task Validate_WhenPasswordEmpty_ShouldReturnInvalid()
    {
        // Arrange
        var request = new LoginUserRequest
        {
            Email = "<EMAIL>",
            Password = ""
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().ContainSingle(e => e.PropertyName == "Password");
    }

    [Test]
    public async Task Validate_WhenBothFieldsInvalid_ShouldReturnMultipleErrors()
    {
        // Arrange
        var request = new LoginUserRequest
        {
            Email = "invalid",
            Password = ""
        };

        // Act
        var result = await _validator.ValidateAsync(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().HaveCount(2);
        result.Errors.Should().Contain(e => e.PropertyName == "Email");
        result.Errors.Should().Contain(e => e.PropertyName == "Password");
    }
}
