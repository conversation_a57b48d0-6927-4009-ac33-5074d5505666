using FluentAssertions;
using FluentValidation.Results;
using Api.Controllers.Auth;
using Application.Common.Interfaces;
using Domain.Dtos;
using Infrastructure.ExternalServices;
using Microsoft.AspNetCore.Mvc;
using Moq;
using NUnit.Framework;

namespace Application.UnitTests.Controllers;

[TestFixture]
public class AuthControllerTests
{
    private Mock<IUserService> _mockUserService;
    private Mock<GoogleOAuthService> _googleOAuthService;
    private AuthController _controller;

    [SetUp]
    public void SetUp()
    {
        _mockUserService = new Mock<IUserService>();
        _googleOAuthService = new Mock<GoogleOAuthService>();


        _controller = new AuthController(
            _mockUserService.Object, _googleOAuthService.Object);
    }

    [Test]
    public async Task Register_WhenValidRequest_ShouldReturnOkWithUserId()
    {
        // Arrange
        var user = new CreateUserDto{
            Name = "Jonh",
            Email     = "<EMAIL>",
            Password  = "password123",
            Phone  = "22999999999"
        };
        
        const int expectedUserId = 123;
        var validationResult = new ValidationResult();
        

        _mockUserService
            .Setup(s => s.RegisterUserAsync(user))
            .ReturnsAsync(Application.Common.Models.Result<int>.Success(expectedUserId));

        // Act
        var result = await _controller.Register(user);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        okResult.Value.Should().BeEquivalentTo(new { message = "User registered successfully", userId = expectedUserId });
    }

    [Test]
    public async Task Register_WhenInvalidRequest_ShouldReturnBadRequest()
    {
        // Arrange
        var user = new CreateUserDto{
            Name = "Jonh",
            Email     = "<EMAIL>",
            Password  = "password123",
            Phone  = "22999999999"
        };

        var validationFailure = new ValidationFailure("Email", "Invalid email format");
        var validationResult = new ValidationResult(new[] { validationFailure });
        
        // Act
        var result = await _controller.Register(user);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().BeEquivalentTo(new { errors = new[] { "Invalid email format" } });
    }

    [Test]
    public async Task Register_WhenServiceReturnsFailure_ShouldReturnBadRequest()
    {
        // Arrange
        var user = new CreateUserDto{
            Name = "Jonh",
            Email     = "<EMAIL>",
            Password  = "password123",
            Phone  = "22999999999"
        };

        var validationResult = new ValidationResult();
        const string expectedError = "User already exists";
        
        _mockUserService
            .Setup(s => s.RegisterUserAsync(user))
            .ReturnsAsync(Application.Common.Models.Result<int>.Failure(expectedError));

        // Act
        var result = await _controller.Register(user);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().BeEquivalentTo(new { errors = new[] { expectedError } });
    }

    [Test]
    public async Task Login_WhenValidCredentials_ShouldReturnOkWithToken()
    {
        // Arrange
        var request = new LoginDto
        {
            Email = "<EMAIL>",
            Password = "password123"
        };

        const string expectedToken = "jwt-token";
        var validationResult = new ValidationResult();
        

        _mockUserService
            .Setup(s => s.LoginUserAsync(request))
            .ReturnsAsync(Application.Common.Models.Result<string>.Success(expectedToken));

        // Act
        var result = await _controller.Login(request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        okResult.Value.Should().BeEquivalentTo(new { token = expectedToken });
    }

    [Test]
    public async Task Login_WhenInvalidRequest_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new LoginDto
        {
            Email = "invalid-email",
            Password = ""
        };

        var validationFailure = new ValidationFailure("Email", "Invalid email format");
        var validationResult = new ValidationResult(new[] { validationFailure });
        

        // Act
        var result = await _controller.Login(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().BeEquivalentTo(new { errors = new[] { "Invalid email format" } });
    }

    [Test]
    public async Task Login_WhenInvalidCredentials_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new LoginDto
        {
            Email = "<EMAIL>",
            Password = "wrongpassword"
        };

        var validationResult = new ValidationResult();
        const string expectedError = "Invalid credentials";
        

        _mockUserService
            .Setup(s => s.LoginUserAsync(request))
            .ReturnsAsync(Application.Common.Models.Result<string>.Failure(expectedError));

        // Act
        var result = await _controller.Login(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().BeEquivalentTo(new { errors = new[] { expectedError } });
    }
}
