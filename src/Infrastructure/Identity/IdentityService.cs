using Application.Common.Interfaces;
using Application.Common.Models;
using Domain.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Identity;

public class IdentityService : IIdentityService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IUserClaimsPrincipalFactory<ApplicationUser> _userClaimsPrincipalFactory;
    private readonly IAuthorizationService _authorizationService;

    public IdentityService(
        UserManager<ApplicationUser> userManager,
        IUserClaimsPrincipalFactory<ApplicationUser> userClaimsPrincipalFactory,
        IAuthorizationService authorizationService)
    {
        _userManager = userManager;
        _userClaimsPrincipalFactory = userClaimsPrincipalFactory;
        _authorizationService = authorizationService;
    }

    public async Task<string?> GetUserNameAsync(string userId)
    {
        var user = await _userManager.FindByIdAsync(userId);

        return user?.UserName;
    }

    public async Task<(Result Result, string UserId)> CreateUserAsync(CreateUserDto data)
    {
        var user = new ApplicationUser
        {
            UserName = data.Email,
            Email = data.Email,
            Name = data.Name
        };

        var result = await _userManager.CreateAsync(user, data.Password);

        return (result.ToApplicationResult(), user.Id);
    }

    public async Task<(Result Result, string UserId, string? Email, IEnumerable<string> Roles)> AuthenticateAsync(LoginDto data)
    {
        var user = await _userManager.Users.FirstOrDefaultAsync(u =>  u.Email == data.Email);
        if (user == null)
        {
            return (Result.Failure(new[] { "Invalid credentials" }), string.Empty, null, Array.Empty<string>());
        }

        var passwordValid = await _userManager.CheckPasswordAsync(user, data.Password);
        if (!passwordValid)
        {
            return (Result.Failure(new[] { "Invalid credentials" }), string.Empty, null, Array.Empty<string>());
        }

        var roles = await _userManager.GetRolesAsync(user);
        return (Result.Success(), user.Id, user.UserName ?? user.Email, roles);
    }

    public async Task<bool> IsInRoleAsync(string userId, string role)
    {
        var user = await _userManager.FindByIdAsync(userId);

        return user != null && await _userManager.IsInRoleAsync(user, role);
    }

    public async Task<bool> AuthorizeAsync(string userId, string policyName)
    {
        var user = await _userManager.FindByIdAsync(userId);

        if (user == null)
        {
            return false;
        }

        var principal = await _userClaimsPrincipalFactory.CreateAsync(user);

        var result = await _authorizationService.AuthorizeAsync(principal, policyName);

        return result.Succeeded;
    }

    public async Task<Result> DeleteUserAsync(string userId)
    {
        var user = await _userManager.FindByIdAsync(userId);

    return user != null ? await DeleteUserInternal(user) : Result.Success();
    }

    public async Task<Result> DeleteUserInternal(ApplicationUser user)
    {
        var result = await _userManager.DeleteAsync(user);

        return result.ToApplicationResult();
    }
}
