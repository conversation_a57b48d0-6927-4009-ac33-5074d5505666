using Application.Common.Models;
using Domain.Dtos;
using HighCapital.Core.Domain.Entities;

namespace Application.Common.Interfaces;

public interface IUserService
{
    Task<Result<int>> RegisterUserAsync(CreateUserDto data);
    Task<Result<string>> LoginUserAsync(LoginDto data);
    Task<Result<User?>> GetUserByIdAsync(int id);
    Task<Result<User?>> GetUserByEmailAsync(string email);
    Task<Result<IEnumerable<User>>> GetAllUsersAsync();
    Task<Result> UpdateUserAsync(int id, CreateUserDto data);
    Task<Result> DeleteUserAsync(int id);
    Task<Result<string>> LoginWithGoogleAsync(string idToken);
}
