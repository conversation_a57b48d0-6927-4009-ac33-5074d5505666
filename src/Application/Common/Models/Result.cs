namespace Application.Common.Models;

public class Result
{
    internal Result(bool succeeded, IEnumerable<string> errors)
    {
        Succeeded = succeeded;
        Errors = errors.ToArray();
    }

    public bool Succeeded { get; init; }
    public string[] Errors { get; init; }
    public bool Failed => !Succeeded;
    public string? FirstError => Errors.FirstOrDefault();

    public static Result Success()
    {
        return new Result(true, Array.Empty<string>());
    }

    public static Result Failure(IEnumerable<string> errors)
    {
        return new Result(false, errors);
    }

    public static Result Failure(string error)
    {
        return new Result(false, new[] { error });
    }

    public static implicit operator Result(string error) => Failure(error);

    public override string ToString()
    {
        return Succeeded ? "Success" : $"Failed: {FirstError}";
    }
}

public class Result<T> : Result
{
    internal Result(bool succeeded, T value, IEnumerable<string> errors) : base(succeeded, errors)
    {
        Value = value;
    }

    public T Value { get; init; }

    public static Result<T> Success(T value)
    {
        return new Result<T>(true, value, Array.Empty<string>());
    }

    public new static Result<T> Failure(IEnumerable<string> errors)
    {
        return new Result<T>(false, default!, errors);
    }

    public new static Result<T> Failure(string error)
    {
        return new Result<T>(false, default!, new[] { error });
    }

    public static implicit operator Result<T>(T value) => Success(value);
    public static implicit operator Result<T>(string error) => Failure(error);

    public override string ToString()
    {
        return Succeeded ? $"Success: {Value}" : $"Failed: {FirstError}";
    }
}
