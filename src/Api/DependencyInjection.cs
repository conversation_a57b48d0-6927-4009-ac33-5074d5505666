using Api.Helpers;
using Application.Common.Interfaces;
using Application.Services;
using Infrastructure.ExternalServices;
using Microsoft.AspNetCore.Mvc;
using Microsoft.OpenApi.Models;

namespace Microsoft.Extensions.DependencyInjection;

public static class DependencyInjection
{
    public static void AddWebServices(this IHostApplicationBuilder builder)
    {
        //services
        builder.Services.AddScoped<IUserService, UserService>();

        //google
        var googleClientId = builder.Configuration["GoogleOAuth:ClientId"];
        var googleClientSecret = builder.Configuration["GoogleOAuth:ClientSecret"];
        var googleRedirectUri = builder.Configuration["GoogleOAuth:RedirectUri"];

        builder.Services.AddSingleton(
            new GoogleOAuthService(googleClientId!, googleClientSecret!, googleRedirectUri!)
        );

        // DbContext já configurado no Infrastructure layer

        builder.Services.AddHttpContextAccessor();
        builder.Services.AddExceptionHandler<CustomExceptionHandler>();
        builder.Services.AddProblemDetails();

        // Add Health Checks
        builder.Services.AddHealthChecks();

        // Customise default API behaviour
        builder.Services.Configure<ApiBehaviorOptions>(options =>
            options.SuppressModelStateInvalidFilter = true
        );

        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc(
                "v1",
                new OpenApiInfo { Title = "HighCapital.AuthenticationService API", Version = "v1" }
            );
            c.AddSecurityDefinition(
                "Bearer",
                new OpenApiSecurityScheme
                {
                    In = ParameterLocation.Header,
                    Description = "Please enter a valid token",
                    Name = "Authorization",
                    Type = SecuritySchemeType.Http,
                    BearerFormat = "JWT",
                    Scheme = "Bearer",
                }
            );
            c.AddSecurityRequirement(
                new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer",
                            },
                        },
                        new string[] { }
                    },
                }
            );
        });
    }
}
